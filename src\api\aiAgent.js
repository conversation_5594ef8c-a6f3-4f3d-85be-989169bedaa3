import request from '@/utils/request.js'
import { fetchEventSource } from '@microsoft/fetch-event-source'

// AI平台后端基础路径
const AI_BASE_PATH = '/data/ai-platform-backend/api/v1'

/**
 * 获取智能体详情
 * @param {string} agentId - 智能体ID
 * @param {boolean} noToLogin - 是否需要登录验证
 * @returns {Promise} 智能体配置信息
 */
export const getAgentDetail = (agentId, noToLogin = false) => {
  return request.get(`${AI_BASE_PATH}/agent/detail/${agentId}`, { noToLogin })
}

/**
 * 获取聊天历史列表
 * @param {string} agentId - 智能体ID
 * @param {boolean} noToLogin - 是否需要登录验证
 * @returns {Promise} 历史记录列表
 */
export const getChatHistoryList = (agentId, noToLogin = false) => {
  return request.get(`${AI_BASE_PATH}/agent/chat/${agentId}/history/all`, { noToLogin })
}

/**
 * 删除历史记录
 * @param {string} historyId - 历史记录ID
 * @returns {Promise} 删除结果
 */
export const deleteChatHistory = (historyId) => {
  return request.delete(`${AI_BASE_PATH}/agent/chat/history/${historyId}`)
}

/**
 * 流式聊天接口
 * @param {string} agentId - 智能体ID
 * @param {Object} requestBody - 请求体
 * @param {Object} callbacks - 回调函数
 * @returns {Promise<AbortController>} 返回AbortController用于取消请求
 */
export const streamChat = async (agentId, requestBody, callbacks = {}) => {
  const {
    onMessage = () => {},
    onError = () => {},
    onOpen = () => {},
    onClose = () => {}
  } = callbacks

  const abortController = new AbortController()
  
  try {
    // 获取认证token
    const token = getAuthToken()
    
    await fetchEventSource(`${AI_BASE_PATH}/agent/chat/${agentId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      },
      body: JSON.stringify(requestBody),
      signal: abortController.signal,
      
      onopen(response) {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        onOpen(response)
      },
      
      onmessage(event) {
        if (event.data === '[DONE]') {
          onClose()
          return
        }
        
        try {
          onMessage(event.data)
        } catch (error) {
          console.error('解析流式数据失败:', error)
          onError(error)
        }
      },
      
      onclose() {
        console.log('流式连接关闭')
        onClose()
      },
      
      onerror(error) {
        console.error('流式请求错误:', error)
        onError(error)
        throw error
      }
    })
  } catch (error) {
    if (error.name !== 'AbortError') {
      console.error('流式聊天失败:', error)
      onError(error)
    }
  }
  
  return abortController
}

/**
 * 获取认证token
 * @returns {string|null} 认证token
 */
function getAuthToken() {
  // 从localStorage或其他地方获取token
  // 这里需要根据项目的实际认证方式来实现
  return localStorage.getItem('auth_token') || 
         localStorage.getItem('ylb-token') ||
         uni.getStorageSync('ylb-cookie')
}

/**
 * 安全的JSON解析
 * @param {string} jsonString - JSON字符串
 * @param {*} fallback - 解析失败时的默认值
 * @returns {*} 解析结果
 */
export const safeJsonParse = (jsonString, fallback = null) => {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.error('JSON解析失败:', error)
    return fallback
  }
}

/**
 * 安全的历史数据解析
 * @param {string|Object} historyData - 历史数据
 * @returns {Array} 解析后的历史数据
 */
export const safeParseHistoryData = (historyData) => {
  try {
    if (typeof historyData === 'string') {
      const jsonStr = historyData.replace(/'/g, '"')
      return JSON.parse(jsonStr)
    }
    return historyData
  } catch (error) {
    console.error('历史数据解析失败:', error)
    return []
  }
}

/**
 * 提取首条消息作为标题
 * @param {string|Object} historyData - 历史数据
 * @returns {string} 提取的标题
 */
export const extractFirstMessage = (historyData) => {
  try {
    const parsedHistory = safeParseHistoryData(historyData)
    if (!Array.isArray(parsedHistory) || parsedHistory.length === 0) {
      return '新对话'
    }
    
    const firstMessage = parsedHistory[0]
    if (!firstMessage) return '新对话'

    if (Array.isArray(firstMessage.content)) {
      return firstMessage.content[0]?.text || firstMessage.content[0] || '新对话'
    }
    return firstMessage.content || '新对话'
  } catch (error) {
    console.error('提取首条消息失败:', error)
    return '解析错误'
  }
}
